; PlatformIO Project Configuration File
;
;cd /Users/<USER>/Documents/PlatformIO/Projects/espdrin
;pio run --target upload --upload-port /dev/cu.usbserial-1120
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps = 
	gyverlibs/GyverBME280@^1.5.3
	adafruit/Adafruit ST7735 and ST7789 Library@^1.10.3
	adafruit/Adafruit GFX Library@^1.11.9
	bblanchon/Ard<PERSON>o<PERSON><PERSON>@^7.4.2
	https://github.com/me-no-dev/AsyncTCP.git
	https://github.com/me-no-dev/ESPAsyncWebServer.git
monitor_speed = 115200
