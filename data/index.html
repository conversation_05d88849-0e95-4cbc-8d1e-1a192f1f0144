<!DOCTYPE html>
<html>
<head>
    <title>Taupunkt-Lueftungssystem</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f0f0f0; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .sensor-box { 
            display: inline-block; 
            width: 45%; 
            margin: 10px; 
            padding: 15px; 
            border-radius: 8px; 
            text-align: center; 
        }
        .indoor { 
            background-color: #e3f2fd; 
            border: 2px solid #2196f3; 
        }
        .outdoor { 
            background-color: #e8f5e8; 
            border: 2px solid #4caf50; 
        }
        .error { 
            background-color: #ffebee; 
            border: 2px solid #f44336; 
        }
        .no-data { 
            background-color: #fff3e0; 
            border: 2px solid #ff9800; 
        }
        .ventilation { 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 8px; 
            text-align: center; 
            font-size: 18px; 
            font-weight: bold; 
        }
        .vent-yes { 
            background-color: #c8e6c9; 
            border: 2px solid #4caf50; 
            color: #2e7d32; 
        }
        .vent-no { 
            background-color: #ffcdd2; 
            border: 2px solid #f44336; 
            color: #c62828; 
        }
        .vent-na { 
            background-color: #fff3e0; 
            border: 2px solid #ff9800; 
            color: #ef6c00; 
        }
        h1 { 
            text-align: center; 
            color: #333; 
        }
        h2 { 
            margin-top: 0; 
        }
        .value { 
            font-size: 24px; 
            font-weight: bold; 
            margin: 5px 0; 
        }
        .unit { 
            font-size: 16px; 
            color: #666; 
        }
        .delta { 
            margin: 20px 0; 
            padding: 10px; 
            background-color: #f5f5f5; 
            border-radius: 5px; 
            text-align: center; 
        }
        .footer { 
            text-align: center; 
            margin-top: 20px; 
            color: #666; 
        }
        .refresh-btn { 
            background-color: #2196f3; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px; 
        }
        .refresh-btn:hover { 
            background-color: #1976d2; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Taupunkt-Lueftungssystem</h1>
        
        <div id="indoor-box" class="sensor-box indoor">
            <h2>INDOOR</h2>
            <div id="indoor-content">
                <div class="value">Lade...</div>
            </div>
        </div>
        
        <div id="outdoor-box" class="sensor-box outdoor">
            <h2>OUTDOOR</h2>
            <div id="outdoor-content">
                <div class="value">Lade...</div>
            </div>
        </div>
        
        <div id="delta-section" class="delta" style="display: none;">
            <strong>Taupunktdifferenz: <span id="delta-tp">0.0</span>°C</strong>
        </div>
        
        <div id="ventilation-section" class="ventilation vent-na" style="display: none;">
            <span id="ventilation-text">Lüftungsempfehlung nicht verfügbar</span>
        </div>
        
        <div class="footer">
            <button class="refresh-btn" onclick="loadData()">Aktualisieren</button><br>
            <div id="status">Lade Daten...</div>
            <div style="margin-top: 10px;">
                <a href="/api/current-data" target="_blank">JSON-Daten anzeigen</a>
            </div>
        </div>
    </div>

    <script>
        function loadData() {
            fetch('/api/current-data')
                .then(response => response.json())
                .then(data => {
                    updateIndoorData(data.indoor);
                    updateOutdoorData(data.outdoor, data.outdoor_data_valid);
                    updateVentilation(data.delta_tp, data.ventilation_recommended, data.outdoor_data_valid);
                    updateStatus(data.uptime_seconds);
                })
                .catch(error => {
                    console.error('Fehler beim Laden der Daten:', error);
                    document.getElementById('status').textContent = 'Fehler beim Laden der Daten';
                });
        }

        function updateIndoorData(indoor) {
            const content = document.getElementById('indoor-content');
            const box = document.getElementById('indoor-box');
            
            if (indoor.temp === 999.0 || indoor.humidity === 999.0 || indoor.dewpoint === 999.0) {
                box.className = 'sensor-box error';
                content.innerHTML = '<div class="value">SENSOR FEHLER</div>';
            } else {
                box.className = 'sensor-box indoor';
                content.innerHTML = `
                    <div class="value">${indoor.temp.toFixed(1)}<span class="unit">°C</span></div>
                    <div>Luftfeuchtigkeit: ${indoor.humidity.toFixed(1)}%</div>
                    <div>Taupunkt: ${indoor.dewpoint.toFixed(1)}°C</div>
                `;
            }
        }

        function updateOutdoorData(outdoor, valid) {
            const content = document.getElementById('outdoor-content');
            const box = document.getElementById('outdoor-box');
            
            if (!valid) {
                box.className = 'sensor-box no-data';
                content.innerHTML = '<div class="value">NO DATA</div>';
            } else if (outdoor.temp === 999.0 || outdoor.humidity === 999.0 || outdoor.dewpoint === 999.0) {
                box.className = 'sensor-box error';
                content.innerHTML = '<div class="value">SENSOR FEHLER</div>';
            } else {
                box.className = 'sensor-box outdoor';
                content.innerHTML = `
                    <div class="value">${outdoor.temp.toFixed(1)}<span class="unit">°C</span></div>
                    <div>Luftfeuchtigkeit: ${outdoor.humidity.toFixed(1)}%</div>
                    <div>Taupunkt: ${outdoor.dewpoint.toFixed(1)}°C</div>
                `;
            }
        }

        function updateVentilation(deltaTp, recommended, valid) {
            const deltaSection = document.getElementById('delta-section');
            const ventilationSection = document.getElementById('ventilation-section');
            const deltaTpSpan = document.getElementById('delta-tp');
            const ventilationText = document.getElementById('ventilation-text');
            
            if (valid) {
                deltaSection.style.display = 'block';
                deltaTpSpan.textContent = deltaTp.toFixed(1);
                
                ventilationSection.style.display = 'block';
                if (recommended) {
                    ventilationSection.className = 'ventilation vent-yes';
                    ventilationText.textContent = 'LÜFTUNG EMPFOHLEN';
                } else {
                    ventilationSection.className = 'ventilation vent-no';
                    ventilationText.textContent = 'LÜFTUNG NICHT EMPFOHLEN';
                }
            } else {
                deltaSection.style.display = 'none';
                ventilationSection.style.display = 'block';
                ventilationSection.className = 'ventilation vent-na';
                ventilationText.textContent = 'Lüftungsempfehlung nicht verfügbar';
            }
        }

        function updateStatus(uptime) {
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            
            document.getElementById('status').textContent = 
                `Laufzeit: ${hours}h ${minutes}m ${seconds}s | Letzte Aktualisierung: ${new Date().toLocaleTimeString()}`;
        }

        // Daten beim Laden der Seite laden
        loadData();
        
        // Alle 10 Sekunden automatisch aktualisieren
        setInterval(loadData, 10000);
    </script>
</body>
</html>
