#ifndef VENTILATION_H
#define VENTILATION_H

// Lüftungslogik-Klasse
class VentilationLogic {
private:
  float delta_tp;
  bool recommendation;
  
public:
  VentilationLogic();
  
  // Lüftungsempfehlung berechnen
  bool calculateRecommendation(float indoor_temp, float outdoor_temp, 
                              float indoor_dewpoint, float outdoor_dewpoint);
  
  // Getter für aktuelle Werte
  float getDeltaTp() const { return delta_tp; }
  bool getRecommendation() const { return recommendation; }
  
  // Lüftungslogik zurücksetzen
  void reset();
};

#endif
