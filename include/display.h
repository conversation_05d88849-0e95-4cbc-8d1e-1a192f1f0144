#ifndef DISPLAY_H
#define DISPLAY_H

#include <Adafruit_ST7789.h>
#include <Adafruit_GFX.h>

// LCD Display Pins
#define LCD_MOSI 23
#define LCD_SCLK 18
#define LCD_CS   15
#define LCD_DC   2
#define LCD_RST  4
#define LCD_BLK  32

// Display-Objekt
extern Adafruit_ST7789 lcd;

// Funktionsdeklarationen
void display_init(void);
void display_show_dual_measurements(float indoor_temp, float indoor_hum, float indoor_tp, 
                                   float outdoor_temp, float outdoor_hum, float outdoor_tp, bool outdoor_valid,
                                   bool lueftung_empfohlen, float delta_tp);

#endif
