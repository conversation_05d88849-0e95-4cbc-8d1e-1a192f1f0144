#ifndef WEB_HANDLER_H
#define WEB_HANDLER_H

#include <WebServer.h>
#include <ArduinoJson.h>
#include "outdoor_data.h"

class WebHandler {
private:
  WebServer* server;
  OutdoorData outdoor_data;
  
public:
  WebHandler(WebServer* webServer);
  
  // WebServer einrichten
  void setup();
  
  // Client-Anfragen verarbeiten
  void handleClient();
  
  // Outdoor-Daten abrufen
  const OutdoorData& getOutdoorData() const { return outdoor_data; }
  
  // Prüfen ob Outdoor-Daten gültig sind (1 Minute)
  bool isOutdoorDataValid() const;
  
private:
  // Hand<PERSON> für POST vom Outdoor ESP
  void handleSensorData();
};

#endif
