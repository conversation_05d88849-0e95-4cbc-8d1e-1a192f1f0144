#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include <GyverBME280.h>
#include <Wire.h>

// I2C Pins für BME280 Sensor
#define SDA_PIN 12
#define SCL_PIN 13

// Sensor-Datenstruktur
struct SensorData {
  float temperature;
  float humidity;
  float dewpoint;
  bool valid;
};

class SensorManager {
private:
  GyverBME280 bme;
  bool sensor_initialized;
  
public:
  SensorManager();
  bool init();
  SensorData readData();
  bool reinitialize();
  bool isInitialized() const { return sensor_initialized; }
};

#endif
