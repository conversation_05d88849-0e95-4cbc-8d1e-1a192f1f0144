#ifndef DATA_LOGGER_H
#define DATA_LOGGER_H

#include <LittleFS.h>

// Logging-Parameter
#define LOG_INTERVAL 5    // Logging alle X Minuten (5 = alle 5 Minuten)
#define LOG_FILENAME "/sensor_data.csv"

class DataLogger {
private:
  unsigned long last_log_time;
  bool logging_enabled;
  
public:
  DataLogger();
  
  // LittleFS und Logging initialisieren
  bool init();
  
  // Sensordaten loggen
  void logSensorData(float indoor_temp, float indoor_hum, float indoor_tp,
                    float outdoor_temp, float outdoor_hum, float outdoor_tp,
                    bool ventilation_recommended, float delta_tp);
  
  // Prüfen ob Logging-Zeit erreicht ist
  bool shouldLog() const;
  
  // Logging aktivieren/deaktivieren
  void setLoggingEnabled(bool enabled) { logging_enabled = enabled; }
  bool isLoggingEnabled() const { return logging_enabled; }
  
private:
  // CSV-Header erstellen
  void createLogHeader();
};

#endif
