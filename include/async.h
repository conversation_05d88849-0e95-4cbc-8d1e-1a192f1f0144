#ifndef ASYNC_H
#define ASYNC_H

#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include "sensor_manager.h"
#include "ventilation.h"

// Forward declaration to avoid circular dependency
class WebHandler;

class WebServerHandler {
private:
    AsyncWebServer server;
    AsyncWebSocket ws;

    // Referenzen zu den anderen Komponenten
    SensorManager* sensorManager;
    WebHandler* webHandler;
    VentilationLogic* ventilationLogic;
    unsigned long startTime;

public:
    WebServerHandler();

    // Initialisierung mit Referenzen zu anderen Komponenten
    void begin(SensorManager* sm, WebHandler* wh, VentilationLogic* vl);

    // WebSocket Event Handler
    void onEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                 void *arg, uint8_t *data, size_t len);

private:
    // Handler für verschiedene Routes
    void handleRoot(AsyncWebServerRequest *request);
    void handleCurrentData(AsyncWebServerRequest *request);
    void handleNotFound(AsyncWebServerRequest *request);
};

#endif