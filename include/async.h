#ifndef ASYNC_H
#define ASYNC_H

#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include "sensor_manager.h"
#include "ventilation.h"
#include "outdoor_data.h"

// Forward declaration to avoid circular dependency
class WebHandler;

class WebServerHandler {
private:
    AsyncWebServer server;
    AsyncWebSocket ws;

    // Referenzen zu den anderen Komponenten
    SensorManager* sensorManager;
    WebHandler* webHandler;
    VentilationLogic* ventilationLogic;
    unsigned long startTime;

    // Aktuelle Sensordaten (werden von main_drin.cpp aktualisiert)
    SensorData currentIndoorData;
    OutdoorData currentOutdoorData;
    bool currentOutdoorValid;
    bool currentVentilationRecommended;
    float currentDeltaTp;

public:
    WebServerHandler();

    // Initialisierung mit Referenzen zu anderen Komponenten
    void begin(SensorManager* sm, WebHandler* wh, VentilationLogic* vl);

    // Aktuelle Sensordaten sammeln (wird von main_drin.cpp aufgerufen)
    void updateCurrentData(const SensorData& indoor, const OutdoorData& outdoor,
                          bool outdoor_valid, bool ventilation_recommended, float delta_tp);

    // WebSocket Event Handler
    void onEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                 void *arg, uint8_t *data, size_t len);

private:
    // Handler für verschiedene Routes
    void handleRoot(AsyncWebServerRequest *request);
    void handleCurrentData(AsyncWebServerRequest *request);
    void handleNotFound(AsyncWebServerRequest *request);
};

#endif