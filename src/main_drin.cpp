#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <ESPmDNS.h>
#include "display.h"
#include "sensor_manager.h"
#include "ventilation.h"
#include "data_logger.h"
#include "web_handler.h"
#include "async.h"

// WLAN
const char* ssid = "Freifunk";
const char* password = "";

// Globale Objekte
SensorManager sensorManager;
VentilationLogic ventilationLogic;
DataLogger dataLogger;
WebServer server(8080);  // Für POST API vom Outdoor ESP
WebHandler webHandler(&server);
WebServerHandler asyncWebServer;  // Für HTML Interface

void setup() {
  Serial.begin(115200);
  Serial.println(F("Indoor ESP startet..."));

  // Display init
  display_init();

  // DataLogger init
  if (!dataLogger.init()) {
    Serial.println("DataLogger Initialisierung fehlgeschlagen!");
  }

  // Sensor init
  if (!sensorManager.init()) {
    Serial.println(F("BME280 Sensor nicht gefunden!"));
    // Display mit Fehleranzeige initialisieren
    display_show_dual_measurements(999.0, 999.0, 999.0, 999.0, 999.0, 999.0, false, false, 0.0);
    while (1);
  }

  // WLAN verbinden
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println();
  Serial.print("Verbunden! IP: ");
  Serial.println(WiFi.localIP());

  // mDNS starten
  if (MDNS.begin("indooresp")) {
    Serial.println("mDNS responder gestartet: http://indooresp.local");
  }

  // WebServer einrichten (für POST API vom Outdoor ESP)
  webHandler.setup();

  // Async WebServer einrichten (für HTML Interface)
  asyncWebServer.begin(&sensorManager, &webHandler, &ventilationLogic);
}

void loop() {
  // WebServer verarbeiten
  webHandler.handleClient();

  // Indoor-Sensordaten lesen
  SensorData indoorData = sensorManager.readData();
  
  // Bei Sensor-Fehlern Neuinitialisierung versuchen
  if (!indoorData.valid && sensorManager.isInitialized()) {
    Serial.println("Sensor-Fehler erkannt! Versuche Neuinitialisierung...");
    if (sensorManager.reinitialize()) {
      indoorData = sensorManager.readData();
    }
  }

  // Outdoor-Daten vom WebHandler abrufen
  const OutdoorData& outdoorData = webHandler.getOutdoorData();
  bool outdoor_data_valid = webHandler.isOutdoorDataValid();

  // Lüftungsempfehlung berechnen
  bool ventilation_recommended = false;
  if (outdoor_data_valid && indoorData.valid && outdoorData.valid) {
    ventilationLogic.calculateRecommendation(
      indoorData.temperature, outdoorData.temperature,
      indoorData.dewpoint, outdoorData.dewpoint
    );
    ventilation_recommended = ventilationLogic.getRecommendation();
  } else {
    ventilationLogic.reset();
  }

  // Indoor-Messungen in Serial ausgeben
  if (indoorData.valid) {
    Serial.print("Indoor T: "); Serial.print(indoorData.temperature, 1);
    Serial.print("°C, LF: "); Serial.print(indoorData.humidity, 1);
    Serial.print("%, TP: "); Serial.print(indoorData.dewpoint, 1);
    Serial.println("°C");
  }
  
  if (outdoor_data_valid && outdoorData.valid) {
    Serial.print("Delta TP: "); Serial.print(ventilationLogic.getDeltaTp(), 1);
    Serial.print("°C, Lüftung: "); Serial.println(ventilation_recommended ? "EMPFOHLEN" : "NICHT EMPFOHLEN");
  }

  // Anzeige auf Display
  display_show_dual_measurements(
    indoorData.temperature, indoorData.humidity, indoorData.dewpoint,
    outdoorData.temperature, outdoorData.humidity, outdoorData.dewpoint,
    outdoor_data_valid, ventilation_recommended, ventilationLogic.getDeltaTp()
  );

  // Logging alle X Minuten
  if (dataLogger.shouldLog()) {
    dataLogger.logSensorData(
      indoorData.temperature, indoorData.humidity, indoorData.dewpoint,
      outdoorData.temperature, outdoorData.humidity, outdoorData.dewpoint,
      ventilation_recommended, ventilationLogic.getDeltaTp()
    );
  }

  delay(10000);
}

