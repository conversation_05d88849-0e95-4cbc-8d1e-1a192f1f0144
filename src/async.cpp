WebServerHandler::WebServerHandler() : server(80), ws("/ws") {}

void WebServerHandler::begin() {
    // LittleFS initialisieren
    if (!LittleFS.begin(true)) {
        Serial.println("Fehler beim Initialisieren von LittleFS!");
        return;
    }

      // Route für die Hauptseite
      server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
        File file = LittleFS.open("/index.html", "r");
        if (!file) {
            request->send(404, "text/plain", "File not found");
            return;
        }
        String html = file.readString();
        file.close();
        request->send(200, "text/html", html);
    });
