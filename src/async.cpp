#include "async.h"
#include "web_handler.h"

WebServerHandler::WebServerHandler() : server(80), ws("/ws"), startTime(0) {
    sensorManager = nullptr;
    webHandler = nullptr;
    ventilationLogic = nullptr;
}

void WebServerHandler::begin(SensorManager* sm, WebHandler* wh, VentilationLogic* vl) {
    sensorManager = sm;
    webHandler = wh;
    ventilationLogic = vl;
    startTime = millis();

    // LittleFS initialisieren
    if (!LittleFS.begin(true)) {
        Serial.println("Fehler beim Initialisieren von LittleFS!");
        return;
    }

    // Route für die Hauptseite
    server.on("/", HTTP_GET, [this](AsyncWebServerRequest *request){
        this->handleRoot(request);
    });

    // API Route für aktuelle Sensordaten
    server.on("/api/current-data", HTTP_GET, [this](AsyncWebServerRequest *request){
        this->handleCurrentData(request);
    });

    // WebSocket Setup
    ws.onEvent([this](AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                      void *arg, uint8_t *data, size_t len) {
        this->onEvent(server, client, type, arg, data, len);
    });
    server.addHandler(&ws);

    // 404 Handler
    server.onNotFound([this](AsyncWebServerRequest *request){
        this->handleNotFound(request);
    });

    // Server starten
    server.begin();
    Serial.println("Async WebServer gestartet auf Port 80");
}

void WebServerHandler::handleRoot(AsyncWebServerRequest *request) {
    // Versuche die HTML-Datei aus LittleFS zu lesen
    File file = LittleFS.open("/index.html", "r");
    if (!file) {
        // Falls nicht vorhanden, sende eine einfache HTML-Seite
        String html = "<!DOCTYPE html><html><head><title>Taupunkt-Lueftungssystem</title>";
        html += "<meta charset='UTF-8'><meta name='viewport' content='width=device-width, initial-scale=1.0'>";
        html += "<style>body{font-family:Arial,sans-serif;margin:20px;background-color:#f0f0f0;}";
        html += ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
        html += ".sensor-box{display:inline-block;width:45%;margin:10px;padding:15px;border-radius:8px;text-align:center;}";
        html += ".indoor{background-color:#e3f2fd;border:2px solid #2196f3;}";
        html += ".outdoor{background-color:#e8f5e8;border:2px solid #4caf50;}";
        html += ".error{background-color:#ffebee;border:2px solid #f44336;}";
        html += ".no-data{background-color:#fff3e0;border:2px solid #ff9800;}";
        html += ".ventilation{margin:20px 0;padding:15px;border-radius:8px;text-align:center;font-size:18px;font-weight:bold;}";
        html += ".vent-yes{background-color:#c8e6c9;border:2px solid #4caf50;color:#2e7d32;}";
        html += ".vent-no{background-color:#ffcdd2;border:2px solid #f44336;color:#c62828;}";
        html += ".vent-na{background-color:#fff3e0;border:2px solid #ff9800;color:#ef6c00;}";
        html += "h1{text-align:center;color:#333;}h2{margin-top:0;}";
        html += ".value{font-size:24px;font-weight:bold;margin:5px 0;}";
        html += ".unit{font-size:16px;color:#666;}";
        html += ".delta{margin:20px 0;padding:10px;background-color:#f5f5f5;border-radius:5px;text-align:center;}";
        html += ".footer{text-align:center;margin-top:20px;color:#666;}";
        html += ".refresh-btn{background-color:#2196f3;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;font-size:16px;margin:10px;}";
        html += ".refresh-btn:hover{background-color:#1976d2;}</style></head><body>";
        html += "<div class='container'><h1>Taupunkt-Lueftungssystem</h1>";
        html += "<div id='indoor-box' class='sensor-box indoor'><h2>INDOOR</h2><div id='indoor-content'><div class='value'>Lade...</div></div></div>";
        html += "<div id='outdoor-box' class='sensor-box outdoor'><h2>OUTDOOR</h2><div id='outdoor-content'><div class='value'>Lade...</div></div></div>";
        html += "<div id='delta-section' class='delta' style='display:none;'><strong>Taupunktdifferenz: <span id='delta-tp'>0.0</span>&deg;C</strong></div>";
        html += "<div id='ventilation-section' class='ventilation vent-na' style='display:none;'><span id='ventilation-text'>Lueftungsempfehlung nicht verfuegbar</span></div>";
        html += "<div class='footer'><button class='refresh-btn' onclick='loadData()'>Aktualisieren</button><br>";
        html += "<div id='status'>Lade Daten...</div><div style='margin-top:10px;'><a href='/api/current-data' target='_blank'>JSON-Daten anzeigen</a></div></div></div>";
        html += "<script>function loadData(){fetch('/api/current-data').then(response=>response.json()).then(data=>{updateIndoorData(data.indoor);updateOutdoorData(data.outdoor,data.outdoor_data_valid);updateVentilation(data.delta_tp,data.ventilation_recommended,data.outdoor_data_valid);updateStatus(data.uptime_seconds);}).catch(error=>{console.error('Fehler beim Laden der Daten:',error);document.getElementById('status').textContent='Fehler beim Laden der Daten';});}";
        html += "function updateIndoorData(indoor){const content=document.getElementById('indoor-content');const box=document.getElementById('indoor-box');if(indoor.temp===999.0||indoor.humidity===999.0||indoor.dewpoint===999.0){box.className='sensor-box error';content.innerHTML='<div class=\"value\">SENSOR FEHLER</div>';}else{box.className='sensor-box indoor';content.innerHTML='<div class=\"value\">'+indoor.temp.toFixed(1)+'<span class=\"unit\">&deg;C</span></div><div>Luftfeuchtigkeit: '+indoor.humidity.toFixed(1)+'%</div><div>Taupunkt: '+indoor.dewpoint.toFixed(1)+'&deg;C</div>';}}";
        html += "function updateOutdoorData(outdoor,valid){const content=document.getElementById('outdoor-content');const box=document.getElementById('outdoor-box');if(!valid){box.className='sensor-box no-data';content.innerHTML='<div class=\"value\">NO DATA</div>';}else if(outdoor.temp===999.0||outdoor.humidity===999.0||outdoor.dewpoint===999.0){box.className='sensor-box error';content.innerHTML='<div class=\"value\">SENSOR FEHLER</div>';}else{box.className='sensor-box outdoor';content.innerHTML='<div class=\"value\">'+outdoor.temp.toFixed(1)+'<span class=\"unit\">&deg;C</span></div><div>Luftfeuchtigkeit: '+outdoor.humidity.toFixed(1)+'%</div><div>Taupunkt: '+outdoor.dewpoint.toFixed(1)+'&deg;C</div>';}}";
        html += "function updateVentilation(deltaTp,recommended,valid){const deltaSection=document.getElementById('delta-section');const ventilationSection=document.getElementById('ventilation-section');const deltaTpSpan=document.getElementById('delta-tp');const ventilationText=document.getElementById('ventilation-text');if(valid){deltaSection.style.display='block';deltaTpSpan.textContent=deltaTp.toFixed(1);ventilationSection.style.display='block';if(recommended){ventilationSection.className='ventilation vent-yes';ventilationText.textContent='LUEFTUNG EMPFOHLEN';}else{ventilationSection.className='ventilation vent-no';ventilationText.textContent='LUEFTUNG NICHT EMPFOHLEN';}}else{deltaSection.style.display='none';ventilationSection.style.display='block';ventilationSection.className='ventilation vent-na';ventilationText.textContent='Lueftungsempfehlung nicht verfuegbar';}}";
        html += "function updateStatus(uptime){const hours=Math.floor(uptime/3600);const minutes=Math.floor((uptime%3600)/60);const seconds=uptime%60;document.getElementById('status').textContent='Laufzeit: '+hours+'h '+minutes+'m '+seconds+'s | Letzte Aktualisierung: '+new Date().toLocaleTimeString();}";
        html += "loadData();setInterval(loadData,10000);</script></body></html>";
        request->send(200, "text/html", html);
        return;
    }
    String html = file.readString();
    file.close();
    request->send(200, "text/html", html);
}

void WebServerHandler::handleCurrentData(AsyncWebServerRequest *request) {
    if (!sensorManager || !webHandler || !ventilationLogic) {
        request->send(500, "application/json", "{\"error\":\"Server not properly initialized\"}");
        return;
    }

    // Aktuelle Sensordaten lesen
    SensorData indoorData = sensorManager->readData();
    const OutdoorData& outdoorData = webHandler->getOutdoorData();
    bool outdoor_data_valid = webHandler->isOutdoorDataValid();

    // Lüftungsempfehlung berechnen
    bool ventilation_recommended = false;
    float delta_tp = 0.0;

    if (outdoor_data_valid && indoorData.valid && outdoorData.valid) {
        ventilationLogic->calculateRecommendation(
            indoorData.temperature, outdoorData.temperature,
            indoorData.dewpoint, outdoorData.dewpoint
        );
        ventilation_recommended = ventilationLogic->getRecommendation();
        delta_tp = ventilationLogic->getDeltaTp();
    }

    // JSON Response erstellen
    JsonDocument doc;

    // Indoor Daten
    doc["indoor"]["temp"] = indoorData.temperature;
    doc["indoor"]["humidity"] = indoorData.humidity;
    doc["indoor"]["dewpoint"] = indoorData.dewpoint;

    // Outdoor Daten
    doc["outdoor"]["temp"] = outdoorData.temperature;
    doc["outdoor"]["humidity"] = outdoorData.humidity;
    doc["outdoor"]["dewpoint"] = outdoorData.dewpoint;
    doc["outdoor_data_valid"] = outdoor_data_valid;

    // Lüftungsempfehlung
    doc["delta_tp"] = delta_tp;
    doc["ventilation_recommended"] = ventilation_recommended;

    // Uptime
    doc["uptime_seconds"] = (millis() - startTime) / 1000;

    String response;
    serializeJson(doc, response);
    request->send(200, "application/json", response);
}

void WebServerHandler::handleNotFound(AsyncWebServerRequest *request) {
    request->send(404, "text/plain", "Not Found");
}

void WebServerHandler::onEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                               void *arg, uint8_t *data, size_t len) {
    switch(type) {
        case WS_EVT_CONNECT:
            Serial.printf("WebSocket client #%u connected from %s\n", client->id(), client->remoteIP().toString().c_str());
            break;
        case WS_EVT_DISCONNECT:
            Serial.printf("WebSocket client #%u disconnected\n", client->id());
            break;
        case WS_EVT_DATA:
            // Hier könnten WebSocket-Nachrichten verarbeitet werden
            break;
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}
