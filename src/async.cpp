#include "async.h"

WebServerHandler::WebServerHandler() : server(80), ws("/ws"), startTime(0) {
    sensorManager = nullptr;
    webHandler = nullptr;
    ventilationLogic = nullptr;

    // Initialisiere Datenstrukturen
    currentIndoorData = {999.0, 999.0, 999.0, false};
    currentOutdoorData = {999.0, 999.0, 999.0, 0, false};
    currentOutdoorValid = false;
    currentVentilationRecommended = false;
    currentDeltaTp = 0.0;
}

void WebServerHandler::begin(SensorManager* sm, WebHandler* wh, VentilationLogic* vl) {
    sensorManager = sm;
    webHandler = wh;
    ventilationLogic = vl;
    startTime = millis();

    // LittleFS initialisieren
    if (!LittleFS.begin(true)) {
        Serial.println("Fehler beim Initialisieren von LittleFS!");
        return;
    }

    // Route für die Hauptseite
    server.on("/", HTTP_GET, [this](AsyncWebServerRequest *request){
        this->handleRoot(request);
    });

    // API Route für aktuelle Sensordaten
    server.on("/api/current-data", HTTP_GET, [this](AsyncWebServerRequest *request){
        this->handleCurrentData(request);
    });

    // WebSocket Setup
    ws.onEvent([this](AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                      void *arg, uint8_t *data, size_t len) {
        this->onEvent(server, client, type, arg, data, len);
    });
    server.addHandler(&ws);

    // 404 Handler
    server.onNotFound([this](AsyncWebServerRequest *request){
        this->handleNotFound(request);
    });

    // Server starten
    server.begin();
    Serial.println("Async WebServer gestartet auf Port 80");
}

void WebServerHandler::handleRoot(AsyncWebServerRequest *request) {
    // HTML-Datei aus LittleFS laden
    File file = LittleFS.open("/index.html", "r");
    if (!file) {
        request->send(404, "text/plain", "index.html not found - please upload filesystem with 'pio run -t uploadfs'");
        return;
    }
    String html = file.readString();
    file.close();
    request->send(200, "text/html", html);
}

void WebServerHandler::updateCurrentData(const SensorData& indoor, const OutdoorData& outdoor,
                                        bool outdoor_valid, bool ventilation_recommended, float delta_tp) {
    currentIndoorData = indoor;
    currentOutdoorData = outdoor;
    currentOutdoorValid = outdoor_valid;
    currentVentilationRecommended = ventilation_recommended;
    currentDeltaTp = delta_tp;
}

void WebServerHandler::handleCurrentData(AsyncWebServerRequest *request) {
    // JSON Response mit den aktuellen Daten erstellen
    JsonDocument doc;

    // Indoor Daten
    doc["indoor"]["temp"] = currentIndoorData.temperature;
    doc["indoor"]["humidity"] = currentIndoorData.humidity;
    doc["indoor"]["dewpoint"] = currentIndoorData.dewpoint;

    // Outdoor Daten
    doc["outdoor"]["temp"] = currentOutdoorData.temperature;
    doc["outdoor"]["humidity"] = currentOutdoorData.humidity;
    doc["outdoor"]["dewpoint"] = currentOutdoorData.dewpoint;
    doc["outdoor_data_valid"] = currentOutdoorValid;

    // Lüftungsempfehlung
    doc["delta_tp"] = currentDeltaTp;
    doc["ventilation_recommended"] = currentVentilationRecommended;

    // Uptime
    doc["uptime_seconds"] = (millis() - startTime) / 1000;

    String response;
    serializeJson(doc, response);
    request->send(200, "application/json", response);
}

void WebServerHandler::handleNotFound(AsyncWebServerRequest *request) {
    request->send(404, "text/plain", "Not Found");
}

void WebServerHandler::onEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type,
                               void *arg, uint8_t *data, size_t len) {
    switch(type) {
        case WS_EVT_CONNECT:
            Serial.printf("WebSocket client #%u connected from %s\n", client->id(), client->remoteIP().toString().c_str());
            break;
        case WS_EVT_DISCONNECT:
            Serial.printf("WebSocket client #%u disconnected\n", client->id());
            break;
        case WS_EVT_DATA:
            // Hier könnten WebSocket-Nachrichten verarbeitet werden
            break;
        case WS_EVT_PONG:
        case WS_EVT_ERROR:
            break;
    }
}
