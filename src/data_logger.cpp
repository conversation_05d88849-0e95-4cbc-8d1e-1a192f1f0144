#include "data_logger.h"

DataLogger::DataLogger() : last_log_time(0), logging_enabled(true) {
}

bool DataLogger::init() {
  if (!LittleFS.begin(true)) {
    Serial.println("LittleFS Mount Failed");
    logging_enabled = false;
    return false;
  }
  
  Serial.println("LittleFS Mount Success");
  
  // Header erstellen falls Datei nicht existiert
  createLogHeader();
  
  return true;
}

void DataLogger::logSensorData(float indoor_temp, float indoor_hum, float indoor_tp,
                              float outdoor_temp, float outdoor_hum, float outdoor_tp,
                              bool ventilation_recommended, float delta_tp) {
  if (!logging_enabled) {
    return;
  }
  
  File file = LittleFS.open(LOG_FILENAME, "a");
  if (!file) {
    Serial.println("Fehler beim Öffnen der Log-Datei");
    return;
  }
  
  // Timestamp erstellen
  String timestamp = String(millis() / 1000); // Sekunden seit Start
  
  // CSV-Zeile erstellen
  String logLine = timestamp + ";" + 
                   String(indoor_temp, 1) + ";" + 
                   String(indoor_hum, 1) + ";" + 
                   String(indoor_tp, 1) + ";" + 
                   String(outdoor_temp, 1) + ";" + 
                   String(outdoor_hum, 1) + ";" + 
                   String(outdoor_tp, 1) + ";" + 
                   String(delta_tp, 1) + ";" + 
                   (ventilation_recommended ? "1" : "0");
  
  file.println(logLine);
  file.close();
  
  Serial.println("Daten gespeichert: " + logLine);
  last_log_time = millis();
}

bool DataLogger::shouldLog() const {
  return logging_enabled && (millis() - last_log_time >= LOG_INTERVAL * 60000);
}

void DataLogger::createLogHeader() {
  if (!LittleFS.exists(LOG_FILENAME)) {
    File file = LittleFS.open(LOG_FILENAME, "w");
    if (file) {
      file.println("Timestamp;Indoor_Temp;Indoor_Hum;Indoor_TP;Outdoor_Temp;Outdoor_Hum;Outdoor_TP;Delta_TP;Lueftung_Empfohlen");
      file.close();
      Serial.println("Log-Header erstellt");
    } else {
      Serial.println("Fehler beim Erstellen des Headers");
      logging_enabled = false;
    }
  }
}
