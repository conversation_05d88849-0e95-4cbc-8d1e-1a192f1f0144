#include "ventilation.h"

VentilationLogic::VentilationLogic() : delta_tp(0.0), recommendation(false) {
}

bool VentilationLogic::calculateRecommendation(float indoor_temp, float outdoor_temp, 
                                              float indoor_dewpoint, float outdoor_dewpoint) {
  // Prüfe ob alle Werte gültig sind
  if (indoor_temp == 999.0 || outdoor_temp == 999.0 || 
      indoor_dewpoint == 999.0 || outdoor_dewpoint == 999.0) {
    recommendation = false;
    delta_tp = 0.0;
    return false;
  }
  
  // Taupunktdifferenz berechnen (Indoor - Outdoor)
  delta_tp = indoor_dewpoint - outdoor_dewpoint;
  
  // Einfache Lüftungslogik:
  // Lüften ist sinnvoll wenn Indoor-Taupunkt höher als Outdoor-Taupunkt
  recommendation = (delta_tp > 0.0);
  
  return true;
}

void VentilationLogic::reset() {
  delta_tp = 0.0;
  recommendation = false;
}
