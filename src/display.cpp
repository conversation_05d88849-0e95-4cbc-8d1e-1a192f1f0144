#include <Arduino.h>
#include "display.h"

// Display-Objekt definieren
Adafruit_ST7789 lcd = Adafruit_ST7789(LCD_CS, LCD_DC, LCD_RST);

void display_init(void) {
  // LCD Display initialisieren
  pinMode(LCD_BLK, OUTPUT);
  digitalWrite(LCD_BLK, HIGH);  // Backlight einschalten
  
  lcd.init(170, 320);  // 170x320 Pixel
  lcd.setRotation(3);  // Display drehen falls nötig
  lcd.fillScreen(ST77XX_BLACK);
  
  lcd.setTextColor(ST77XX_WHITE);
  lcd.setTextSize(2);
  lcd.setCursor(20, 50);
  lcd.print(F("BME280 Test..."));
}


void display_show_dual_measurements(float indoor_temp, float indoor_hum, float indoor_tp, 
                                   float outdoor_temp, float outdoor_hum, float outdoor_tp, bool outdoor_valid,
                                   bool lueftung_empfohlen, float delta_tp) {
  // Display löschen
  lcd.fillScreen(ST77XX_BLACK);
  
  // Indoor Daten (linke Seite)
  if (indoor_temp == 999.0 || indoor_hum == 999.0 || indoor_tp == 999.0) {
    // Sensor-Fehler anzeigen
    lcd.setTextColor(ST77XX_RED);
    lcd.setTextSize(2);
    lcd.setCursor(10, 5);
    lcd.print(F("INDOOR"));
    lcd.setTextSize(2);
    lcd.setCursor(10, 25);
    lcd.print(F("SENSOR"));
    lcd.setCursor(10, 45);
    lcd.print(F("FEHLER"));
  } else {
    // Normale Werte anzeigen
    lcd.setTextColor(ST77XX_CYAN);
    lcd.setTextSize(2);
    lcd.setCursor(10, 5);
    lcd.print(F("INDOOR"));
    lcd.setTextSize(2);
    lcd.setCursor(10, 25);
    lcd.print(F("T: "));
    lcd.print(indoor_temp, 1);
    lcd.print(F("C"));
    lcd.setTextSize(2);
    lcd.setCursor(10, 45);
    lcd.print(F("LF: "));
    lcd.print(indoor_hum, 1);
    lcd.print(F("%"));
    lcd.setTextSize(2);
    lcd.setCursor(10, 65);
    lcd.print(F("TP: "));
    lcd.print(indoor_tp, 1);
    lcd.print(F("C"));
  }
  
  // Outdoor Daten (rechte Seite)
  if (outdoor_valid) {
    // Prüfe ob Outdoor-Sensor Fehlerwerte sendet
    if (outdoor_temp == 999.0 || outdoor_hum == 999.0 || outdoor_tp == 999.0) {
      // Outdoor-Sensor Fehler
      lcd.setTextColor(ST77XX_RED);
      lcd.setTextSize(2);
      lcd.setCursor(160, 5);
      lcd.print(F("OUTDOOR"));
      lcd.setTextSize(2);
      lcd.setCursor(160, 25);
      lcd.print(F("SENSOR"));
      lcd.setCursor(160, 45);
      lcd.print(F("FEHLER"));
    } else {
      // Normale Outdoor-Werte
      lcd.setTextColor(ST77XX_GREEN);
      lcd.setTextSize(2);
      lcd.setCursor(160, 5);
      lcd.print(F("OUTDOOR"));
      lcd.setTextSize(2);
      lcd.setCursor(160, 25);
      lcd.print(F("T: "));
      lcd.print(outdoor_temp, 1);
      lcd.print(F("C"));
      lcd.setTextSize(2);
      lcd.setCursor(160, 45);
      lcd.print(F("LF: "));
      lcd.print(outdoor_hum, 1);
      lcd.print(F("%"));
      lcd.setTextSize(2);
      lcd.setCursor(160, 65);
      lcd.print(F("TP: "));
      lcd.print(outdoor_tp, 1);
      lcd.print(F("C"));
    }
  } else {
    // Keine Verbindung zum Outdoor ESP
    lcd.setTextColor(ST77XX_ORANGE);
    lcd.setTextSize(2);
    lcd.setCursor(160, 25);
    lcd.print(F("OUTDOOR"));
    lcd.setCursor(160, 45);
    lcd.print(F("NO DATA"));
  }
  
  // Lüftungsempfehlung am unteren Display-Bereich
  if (outdoor_valid && indoor_temp != 999.0 && outdoor_temp != 999.0) {
    // Taupunktdifferenz anzeigen
    lcd.setTextColor(ST77XX_WHITE);
    lcd.setTextSize(1);
    lcd.setCursor(10, 100);
    lcd.print(F("Delta TP: "));
    lcd.print(delta_tp, 1);
    lcd.print(F("C"));
    
    // Lüftungsempfehlung anzeigen
    if (lueftung_empfohlen) {
      lcd.setTextColor(ST77XX_GREEN);
      lcd.setTextSize(2);
      lcd.setCursor(10, 120);
      lcd.print(F("LUEFTUNG"));
      lcd.setCursor(10, 140);
      lcd.print(F("EMPFOHLEN"));
    } else {
      lcd.setTextColor(ST77XX_RED);
      lcd.setTextSize(2);
      lcd.setCursor(10, 120);
      lcd.print(F("LUEFTUNG"));
      lcd.setCursor(10, 140);
      lcd.print(F("NICHT EMPF."));
    }
  } else {
    // Keine gültigen Daten für Lüftungsempfehlung
    lcd.setTextColor(ST77XX_ORANGE);
    lcd.setTextSize(1);
    lcd.setCursor(10, 100);
    lcd.print(F("Lueftungsempfehlung"));
    lcd.setCursor(10, 115);
    lcd.print(F("nicht verfuegbar"));
  }
}



