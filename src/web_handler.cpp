#include "web_handler.h"

WebHandler::WebHandler(WebServer* webServer) : server(webServer) {
  outdoor_data.temperature = 999.0;
  outdoor_data.humidity = 999.0;
  outdoor_data.dewpoint = 999.0;
  outdoor_data.timestamp = 0;
  outdoor_data.valid = false;
}

void WebHandler::setup() {
  server->on("/api/sensor-data", HTTP_POST, [this]() { this->handleSensorData(); });
  server->onNotFound([this]() {
    server->send(404, "text/plain", "Not Found");
  });
  server->begin();
  Serial.println("WebServer gestartet auf Port 8080");
}

void WebHandler::handleClient() {
  server->handleClient();
}

bool WebHandler::isOutdoorDataValid() const {
  return (millis() - outdoor_data.timestamp < 60000); // 1 Minute gültig
}

void WebHandler::handleSensorData() {
  String body = server->arg("plain");
  Serial.print("POST empfangen: ");
  Serial.println(body);

  JsonDocument doc;
  DeserializationError error = deserializeJson(doc, body);
  if (!error && doc["temp0"].is<float>() && doc["hum0"].is<float>() && doc["tp0"].is<float>()) {
    outdoor_data.temperature = doc["temp0"];
    outdoor_data.humidity = doc["hum0"];
    outdoor_data.dewpoint = doc["tp0"];
    outdoor_data.timestamp = millis();
    outdoor_data.valid = true;

    // Prüfe ob Outdoor-Sensor Fehlerwerte sendet
    if (outdoor_data.temperature == 999.0 || outdoor_data.humidity == 999.0 || outdoor_data.dewpoint == 999.0) {
      Serial.println("Outdoor-Sensor meldet Fehler!");
    } else {
      Serial.print("Outdoor T: "); Serial.print(outdoor_data.temperature, 1);
      Serial.print("°C, LF: "); Serial.print(outdoor_data.humidity, 1);
      Serial.print("%, TP: "); Serial.print(outdoor_data.dewpoint, 1);
      Serial.println("°C");
    }

    server->send(200, "application/json", "{\"status\":\"ok\"}");
  } else {
    Serial.println("Ungültiges JSON!");
    server->send(400, "application/json", "{\"status\":\"error\"}");
  }
}
