#include "sensor_manager.h"
#include <math.h>
#include "taupunkt.h"

SensorManager::SensorManager() : sensor_initialized(false) {
}

bool SensorManager::init() {
  // I2C initialisieren
  Wire.begin(SDA_PIN, SCL_PIN);
  
  // BME280 konfigurieren
  bme.setFilter(FILTER_COEF_16);
  bme.setTempOversampling(OVERSAMPLING_16);
  bme.setHumOversampling(OVERSAMPLING_16);
  bme.setStandbyTime(STANDBY_1000MS);
  
  // Sensor initialisieren
  if (!bme.begin()) {
    Serial.println(F("BME280 Sensor nicht gefunden!"));
    sensor_initialized = false;
    return false;
  }
  
  sensor_initialized = true;
  return true;
}

SensorData SensorManager::readData() {
  SensorData data;
  data.valid = false;
  data.temperature = 999.0;
  data.humidity = 999.0;
  data.dewpoint = 999.0;
  
  if (!sensor_initialized) {
    return data;
  }
  
  // Sensor-Werte auslesen
  float temp = bme.readTemperature();
  float hum = bme.readHumidity();
  
  // Sensor-Fehler prüfen
  if (isnan(temp) || isnan(hum) || temp == 0.0 || hum == 0.0 || 
      temp < -40.0 || temp > 80.0 || hum < 0.0 || hum > 100.0) {
    Serial.println("BME280 Sensor-Fehler erkannt!");
    return data;
  }
  
  // Gültige Werte setzen
  data.temperature = temp;
  data.humidity = hum;
  data.dewpoint = taupunkt(temp, hum);
  data.valid = true;
  
  return data;
}

bool SensorManager::reinitialize() {
  Serial.println("Versuche Sensor-Neuinitialisierung...");
  
  // I2C neu starten
  Wire.end();
  delay(100);
  Wire.begin(SDA_PIN, SCL_PIN);
  delay(100);
  
  // BME280 neu konfigurieren
  bme.setFilter(FILTER_COEF_16);
  bme.setTempOversampling(OVERSAMPLING_16);
  bme.setHumOversampling(OVERSAMPLING_16);
  bme.setStandbyTime(STANDBY_1000MS);
  
  // Sensor neu initialisieren
  if (!bme.begin()) {
    Serial.println("Sensor-Neuinitialisierung fehlgeschlagen!");
    sensor_initialized = false;
    return false;
  }
  
  Serial.println("Sensor erfolgreich neu initialisiert!");
  sensor_initialized = true;
  return true;
}
